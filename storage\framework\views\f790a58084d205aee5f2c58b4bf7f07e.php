

<?php $__env->startSection('title', 'Dashboard - Escape Matrix Academy'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-black py-12">
    <div class="container mx-auto px-4">
        <!-- Welcome Section -->
        <div class="mb-8">
            <h1 class="text-3xl md:text-4xl font-bold text-white mb-2">
                Welcome back, <span class="text-red-500"><?php echo e($user->name); ?></span>
            </h1>
            <p class="text-gray-400">Continue your transformation journey</p>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-red-600 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-2xl font-bold text-white"><?php echo e($stats['enrolled_courses']); ?></p>
                        <p class="text-gray-400 text-sm">Enrolled Courses</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-600 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-2xl font-bold text-white"><?php echo e($stats['completed_courses']); ?></p>
                        <p class="text-gray-400 text-sm">Completed</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-600 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-2xl font-bold text-white"><?php echo e($stats['hours_learned']); ?></p>
                        <p class="text-gray-400 text-sm">Hours Learned</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-600 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-2xl font-bold text-white"><?php echo e($stats['certificates']); ?></p>
                        <p class="text-gray-400 text-sm">Certificates</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Courses -->
        <?php if($recentCourses->count() > 0): ?>
            <div class="mb-12">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-white">Continue Learning</h2>
                    <a href="<?php echo e(route('my-courses')); ?>" class="text-red-500 hover:text-red-400 transition-colors">View All</a>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php $__currentLoopData = $recentCourses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $enrollment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-gray-900 border border-gray-800 rounded-lg overflow-hidden hover:border-red-500 transition-colors">
                            <img src="<?php echo e($enrollment->course->image ? asset('storage/' . $enrollment->course->image) : asset('images/course-template.svg')); ?>" 
                                 alt="<?php echo e($enrollment->course->title); ?>" 
                                 class="w-full h-32 object-cover">
                            
                            <div class="p-4">
                                <h3 class="font-bold text-white mb-2"><?php echo e($enrollment->course->title); ?></h3>
                                <div class="mb-3">
                                    <div class="flex justify-between text-sm text-gray-400 mb-1">
                                        <span>Progress</span>
                                        <span><?php echo e($enrollment->progress ?? 0); ?>%</span>
                                    </div>
                                    <div class="w-full bg-gray-700 rounded-full h-2">
                                        <div class="bg-red-600 h-2 rounded-full" style="width: <?php echo e($enrollment->progress ?? 0); ?>%"></div>
                                    </div>
                                </div>
                                <a href="<?php echo e(route('my-courses.view', $enrollment->course)); ?>" 
                                   class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md transition-colors inline-block text-center text-sm">
                                    Continue Learning
                                </a>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Quick Actions -->
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
            <h2 class="text-2xl font-bold text-white mb-6">Quick Actions</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="<?php echo e(route('courses.index')); ?>" class="flex items-center p-4 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors">
                    <svg class="w-8 h-8 text-red-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    <div>
                        <h3 class="font-semibold text-white">Browse Courses</h3>
                        <p class="text-gray-400 text-sm">Discover new skills</p>
                    </div>
                </a>

                <a href="<?php echo e(route('my-courses')); ?>" class="flex items-center p-4 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors">
                    <svg class="w-8 h-8 text-red-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                    <div>
                        <h3 class="font-semibold text-white">My Courses</h3>
                        <p class="text-gray-400 text-sm">Continue learning</p>
                    </div>
                </a>

                <a href="<?php echo e(route('profile.show')); ?>" class="flex items-center p-4 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors">
                    <svg class="w-8 h-8 text-red-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <div>
                        <h3 class="font-semibold text-white">Profile</h3>
                        <p class="text-gray-400 text-sm">Update your info</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\escapematrix\resources\views/dashboard.blade.php ENDPATH**/ ?>