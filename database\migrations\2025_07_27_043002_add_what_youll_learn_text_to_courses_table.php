<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            // Add new field for paragraph-style "What You'll Learn" content
            // This is separate from the existing 'what_you_will_learn' array field
            $table->longText('what_youll_learn_text')->nullable()->after('what_you_will_learn');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            $table->dropColumn('what_youll_learn_text');
        });
    }
};
