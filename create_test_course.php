<?php

// Simple test to create a course with category
try {
    require 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

    // Create a test course with category
    $course = new \App\Models\Course();
    $course->id = \Illuminate\Support\Str::uuid();
    $course->title = 'Test Course for Category Display';
    $course->subtitle = 'Testing category functionality';
    $course->slug = 'test-course-category';
    $course->description = 'This is a test course to verify category display';
    $course->instructor_id = \App\Models\User::where('email', '<EMAIL>')->first()->id ?? null;
    $course->status = 'published';
    $course->featured = true;
    $course->level = 'beginner';
    $course->language = 'English';
    $course->price = 99.99;
    $course->what_you_will_learn = ['Test learning outcome'];
    $course->requirements = ['Basic knowledge'];
    $course->target_audience = ['Students'];
    
    // Try to assign a category
    $category = \App\Models\CourseCategory::where('slug', 'programming')->first();
    if ($category) {
        $course->category_id = $category->id;
        $course->category = $category->name;
        echo "Assigned category: {$category->name}\n";
    } else {
        $course->category = 'General';
        echo "No category found, using General\n";
    }
    
    $course->save();
    echo "Test course created successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
