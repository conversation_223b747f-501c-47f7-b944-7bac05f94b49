<?php

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Course;
use App\Models\CourseCategory;

echo "=== COURSE CATEGORIES ===\n";
$categories = CourseCategory::all();
foreach($categories as $cat) {
    echo "ID: {$cat->id}, Name: {$cat->name}, Slug: {$cat->slug}\n";
}

echo "\n=== ALL COURSES ===\n";
$courses = Course::with('category')->get();
foreach($courses as $course) {
    echo "Title: {$course->title}\n";
    echo "Status: {$course->status}, Featured: " . ($course->featured ? 'true' : 'false') . "\n";
    echo "Category (string): {$course->category}\n";
    echo "Category ID: {$course->category_id}\n";
    echo "Category Relationship: " . ($course->category ? $course->category->name : 'null') . "\n";
    echo "Created: {$course->created_at}\n";
    echo "---\n";
}

echo "\n=== FEATURED PUBLISHED COURSES FOR HOMEPAGE ===\n";
$featuredCourses = Course::where('featured', true)->where('status', 'published')->get();
echo "Count: " . $featuredCourses->count() . "\n";
foreach($featuredCourses as $course) {
    echo "- {$course->title} ({$course->status})\n";
}

echo "\n=== PUBLISHED COURSES (ALL) ===\n";
$publishedCourses = Course::where('status', 'published')->get();
echo "Count: " . $publishedCourses->count() . "\n";
foreach($publishedCourses as $course) {
    echo "- {$course->title} (Featured: " . ($course->featured ? 'true' : 'false') . ")\n";
}
