<?php

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Course;
use App\Models\CourseCategory;
use App\Models\User;

echo "=== Course and Category Status ===\n\n";

// Check if categories exist
$categoryCount = CourseCategory::count();
echo "Total categories: {$categoryCount}\n";

if ($categoryCount > 0) {
    echo "Categories:\n";
    $categories = CourseCategory::where('parent_id', null)->get();
    foreach ($categories as $category) {
        echo "- {$category->name} (slug: {$category->slug})\n";
    }
} else {
    echo "No categories found!\n";
}

// Check if courses exist
$courseCount = Course::count();
echo "\nTotal courses: {$courseCount}\n";

if ($courseCount > 0) {
    echo "Courses:\n";
    $courses = Course::with(['category'])->get();
    foreach ($courses as $course) {
        $categoryName = $course->category ? $course->category->name : 'No category';
        echo "- {$course->title} (Category: {$categoryName}, Status: {$course->status})\n";
    }
} else {
    echo "No courses found!\n";
}

// Check if instructor exists
$instructor = User::where('email', '<EMAIL>')->first();
if ($instructor) {
    echo "\nInstructor found: {$instructor->name}\n";
} else {
    echo "\nNo instructor found!\n";
}

echo "\n=== End Status ===\n";
