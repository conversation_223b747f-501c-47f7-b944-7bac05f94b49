<?php

namespace App\Console\Commands;

use App\Models\Course;
use App\Models\CourseCategory;
use Illuminate\Console\Command;

class MigrateCourseCategories extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'courses:migrate-categories';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate existing courses from old string-based categories to new category system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Migrating existing courses to new category system...');

        // Get all courses that still use old string-based categories
        $courses = Course::whereNull('category_id')->get();

        if ($courses->isEmpty()) {
            $this->info('No courses need category migration.');
            return;
        }

        // Get all available categories
        $categories = CourseCategory::whereNull('parent_id')->get();

        if ($categories->isEmpty()) {
            $this->error('No categories found! Please run the database seeder first.');
            return;
        }

        // Create a mapping of old category names to new category slugs
        $categoryMapping = [
            'general' => 'business',
            'programming' => 'programming',
            'business' => 'business',
            'design' => 'design-creativity',
            'marketing' => 'marketing',
            'technology' => 'ai-tech',
            'ai' => 'ai-tech',
            'tech' => 'ai-tech',
            'development' => 'programming',
            'web development' => 'programming',
            'data science' => 'data-science',
            'finance' => 'finance-accounting',
            'health' => 'health-fitness',
            'fitness' => 'health-fitness',
            'music' => 'music',
            'photography' => 'photography-video',
            'video' => 'photography-video',
            'lifestyle' => 'lifestyle',
            'personal development' => 'personal-development',
            'teaching' => 'teaching-academics'
        ];

        $migratedCount = 0;

        foreach ($courses as $course) {
            $oldCategory = strtolower(trim($course->category));

            // Find matching category
            $newCategorySlug = $categoryMapping[$oldCategory] ?? 'business'; // Default to business
            $newCategory = $categories->firstWhere('slug', $newCategorySlug);

            if ($newCategory) {
                $course->update([
                    'category_id' => $newCategory->id,
                    'category' => $newCategory->name // Update the old field too for backward compatibility
                ]);

                $this->info("✅ Migrated course '{$course->title}' from '{$oldCategory}' to '{$newCategory->name}'");
                $migratedCount++;
            } else {
                // Fallback to first available category
                $fallbackCategory = $categories->first();
                if ($fallbackCategory) {
                    $course->update([
                        'category_id' => $fallbackCategory->id,
                        'category' => $fallbackCategory->name
                    ]);
                    $this->warn("⚠️  Migrated course '{$course->title}' to fallback category '{$fallbackCategory->name}'");
                    $migratedCount++;
                }
            }
        }

        $this->info("✅ Course category migration completed! Migrated {$migratedCount} courses.");

        // Show summary of categories with course counts
        $this->info("\nCategory Summary:");
        $categoriesWithCounts = CourseCategory::whereNull('parent_id')
            ->withCount(['courses' => function($query) {
                $query->where('status', 'published');
            }])
            ->get();

        foreach ($categoriesWithCounts as $category) {
            $this->line("  {$category->name}: {$category->courses_count} published courses");
        }
    }
}
