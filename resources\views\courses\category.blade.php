@extends('layouts.app')

@section('title', $category->name . ' Courses - Escape Matrix Academy')

@section('content')
<!-- Category Hero Section -->
<section class="py-20 bg-gradient-to-br from-black via-gray-900 to-black">
    <div class="container mx-auto px-4">
        <div class="text-center">
            <div class="inline-flex items-center px-4 py-2 bg-red-600/20 border border-red-600/30 rounded-full text-red-400 text-sm mb-6">
                <span class="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                {{ $courses->total() }} {{ Str::plural('Course', $courses->total()) }} Found
            </div>
            
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
                {{ $category->name }}
                <span class="text-red-500">Courses</span>
            </h1>

            <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                {{ $category->description ?: 'Master ' . strtolower($category->name) . ' with our comprehensive courses designed to transform your skills and accelerate your success.' }}
            </p>
        </div>
    </div>
</section>

<!-- Breadcrumb -->
<section class="py-6 bg-gray-900 border-b border-gray-800">
    <div class="container mx-auto px-4">
        <nav class="flex items-center space-x-2 text-sm">
            <a href="{{ route('home') }}" class="text-gray-400 hover:text-white transition-colors">Home</a>
            <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <a href="{{ route('courses.index') }}" class="text-gray-400 hover:text-white transition-colors">Courses</a>
            <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <span class="text-white">{{ $category->name }}</span>
        </nav>
    </div>
</section>

<!-- Courses Grid -->
<section class="py-16 bg-black">
    <div class="container mx-auto px-4">
        @if($courses->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($courses as $course)
                    <div class="bg-gray-800 border border-gray-700 rounded-lg overflow-hidden hover:border-red-500 transition-all duration-300 group">
                        <div class="relative">
                            <img src="{{ $course->image_url ?: 'https://via.placeholder.com/300x200' }}"
                                 alt="{{ $course->title }}"
                                 class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                            <div class="absolute top-4 left-4">
                                <span class="bg-red-600 text-white px-2 py-1 rounded text-sm">
                                    @if($course->category && is_object($course->category))
                                        {{ $course->category->name }}
                                    @elseif($course->category)
                                        {{ $course->category }}
                                    @else
                                        General
                                    @endif
                                </span>
                            </div>
                            <div class="absolute top-4 right-4">
                                <span class="bg-black/70 text-white px-2 py-1 rounded text-sm">{{ $course->formatted_price }}</span>
                            </div>
                            @if($course->featured)
                                <div class="absolute bottom-4 left-4">
                                    <span class="bg-yellow-600 text-white px-2 py-1 rounded text-xs font-medium">FEATURED</span>
                                </div>
                            @endif
                        </div>

                        <div class="p-6">
                            <h3 class="text-xl font-bold text-white mb-2 group-hover:text-red-500 transition-colors">{{ $course->title }}</h3>
                            <p class="text-gray-400 mb-4 line-clamp-2">{{ $course->description }}</p>

                            <div class="flex items-center gap-4 text-sm text-gray-500 mb-4">
                                <div class="flex items-center gap-1">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    {{ ucfirst($course->level) }}
                                </div>
                                <div class="flex items-center gap-1">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    {{ $course->duration }}
                                </div>
                                @if($course->instructor)
                                    <div class="flex items-center gap-1">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                        {{ $course->instructor->name }}
                                    </div>
                                @endif
                            </div>

                            <a href="{{ route('courses.show', $course) }}" class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md transition-colors inline-block text-center">
                                Learn More
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            @if($courses->hasPages())
                <div class="mt-12 flex justify-center">
                    {{ $courses->links() }}
                </div>
            @endif
        @else
            <!-- No Courses Found -->
            <div class="text-center py-16">
                <div class="text-6xl mb-6">📚</div>
                <h3 class="text-2xl font-bold text-white mb-4">No {{ ucfirst($category) }} Courses Found</h3>
                <p class="text-gray-400 mb-8 max-w-md mx-auto">
                    We're working on adding more {{ strtolower($category) }} courses. Check back soon or explore our other categories.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('courses.index') }}" class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-md transition-colors">
                        Browse All Courses
                    </a>
                    <a href="{{ route('contact') }}" class="border border-gray-600 text-gray-300 hover:bg-gray-800 px-6 py-3 rounded-md transition-colors">
                        Request Course
                    </a>
                </div>
            </div>
        @endif
    </div>
</section>

<!-- Related Categories -->
<section class="py-16 bg-gray-900">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-white mb-4">Explore Other Categories</h2>
            <p class="text-gray-400">Discover more ways to transform your skills and knowledge</p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            @php
                $allCategories = ['Marketing', 'Business', 'Finance', 'Technology', 'Design', 'Personal Development'];
                $otherCategories = array_filter($allCategories, fn($cat) => strtolower($cat) !== strtolower($category));
            @endphp
            
            @foreach(array_slice($otherCategories, 0, 5) as $otherCategory)
                <a href="{{ route('courses.category', strtolower($otherCategory)) }}" 
                   class="bg-gray-800 border border-gray-700 rounded-lg p-4 text-center hover:border-red-500 transition-all duration-300 group">
                    <div class="text-2xl mb-2">
                        @switch(strtolower($otherCategory))
                            @case('marketing')
                                📈
                                @break
                            @case('business')
                                💼
                                @break
                            @case('finance')
                                💰
                                @break
                            @case('technology')
                                💻
                                @break
                            @case('design')
                                🎨
                                @break
                            @case('personal development')
                                🧠
                                @break
                            @default
                                📚
                        @endswitch
                    </div>
                    <h3 class="text-white font-medium group-hover:text-red-500 transition-colors">{{ $otherCategory }}</h3>
                </a>
            @endforeach
            
            <a href="{{ route('courses.index') }}" 
               class="bg-red-600 hover:bg-red-700 rounded-lg p-4 text-center transition-colors">
                <div class="text-2xl mb-2">🔍</div>
                <h3 class="text-white font-medium">View All</h3>
            </a>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endpush
