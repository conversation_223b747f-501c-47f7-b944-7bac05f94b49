<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use App\Models\Course;
use App\Models\CourseCategory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class DatabaseSeeder extends Seeder
{
    /**
     * Generate a secure password with mixed case, numbers, and special characters.
     */
    private function generateSecurePassword(): string
    {
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $numbers = '0123456789';
        $special = '!@#$%^&*';
        
        // Ensure at least one character from each required set
        $password = '';
        $password .= $uppercase[rand(0, strlen($uppercase) - 1)];
        $password .= $lowercase[rand(0, strlen($lowercase) - 1)];
        $password .= $numbers[rand(0, strlen($numbers) - 1)];
        $password .= $special[rand(0, strlen($special) - 1)];
        
        // Fill remaining length with random characters from all sets
        $allChars = $uppercase . $lowercase . $numbers . $special;
        for ($i = 4; $i < 12; $i++) {
            $password .= $allChars[rand(0, strlen($allChars) - 1)];
        }
        
        // Shuffle the password to randomize character positions
        return str_shuffle($password);
    }

    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->command->info('🌱 Starting Laravel LMS Database Seeding...');

        // Create roles
        $this->createRoles();

        // Create permissions
        $this->createPermissions();

        // Create course categories
        $this->createCourseCategories();

        // Create test users
        $this->createTestUsers();

        // Create sample courses with full content
        // $this->call(CourseSeeder::class); // Temporarily disabled for testing

        // Create additional students and enrollments
        $this->createAdditionalStudents();

        // Create sample enrollments and payments
        $this->createSampleEnrollments();

        // Migrate existing courses to new category system
        $this->migrateCourseCategories();

        $this->command->info('✅ Database seeding completed!');
        $this->command->info('🎓 Your Laravel LMS is ready for fresh setup!');
    }

    private function createRoles()
    {
        $this->command->info('Creating roles...');

        $roles = [
            [
                'name' => 'student',
                'display_name' => 'Student',
                'description' => 'Students can enroll in courses and access learning materials',
                'priority' => 1
            ],
            [
                'name' => 'instructor',
                'display_name' => 'Instructor',
                'description' => 'Instructors can create and manage courses',
                'priority' => 2
            ],
            [
                'name' => 'admin',
                'display_name' => 'Administrator',
                'description' => 'Administrators can manage users and moderate content',
                'priority' => 3
            ],
            [
                'name' => 'superadmin',
                'display_name' => 'Super Administrator',
                'description' => 'Super administrators have full system access',
                'priority' => 4
            ]
        ];

        foreach ($roles as $roleData) {
            Role::firstOrCreate(
                ['name' => $roleData['name']],
                $roleData
            );
        }
    }

    private function createPermissions()
    {
        $this->command->info('Creating permissions...');

        $permissions = [
            // Course permissions
            ['name' => 'courses.view', 'display_name' => 'View Courses', 'description' => 'View course listings'],
            ['name' => 'courses.create', 'display_name' => 'Create Courses', 'description' => 'Create new courses'],
            ['name' => 'courses.edit', 'display_name' => 'Edit Courses', 'description' => 'Edit existing courses'],
            ['name' => 'courses.delete', 'display_name' => 'Delete Courses', 'description' => 'Delete courses'],
            ['name' => 'courses.publish', 'display_name' => 'Publish Courses', 'description' => 'Publish courses'],

            // User management permissions
            ['name' => 'users.view', 'display_name' => 'View Users', 'description' => 'View user listings'],
            ['name' => 'users.create', 'display_name' => 'Create Users', 'description' => 'Create new users'],
            ['name' => 'users.edit', 'display_name' => 'Edit Users', 'description' => 'Edit user profiles'],
            ['name' => 'users.delete', 'display_name' => 'Delete Users', 'description' => 'Delete users'],

            // Payment permissions
            ['name' => 'payments.view', 'display_name' => 'View Payments', 'description' => 'View payment records'],
            ['name' => 'payments.manage', 'display_name' => 'Manage Payments', 'description' => 'Manage payment settings'],

            // System permissions
            ['name' => 'system.admin', 'display_name' => 'System Administration', 'description' => 'Full system access'],
        ];

        foreach ($permissions as $permissionData) {
            Permission::firstOrCreate(
                ['name' => $permissionData['name']],
                $permissionData
            );
        }

        // Assign permissions to roles
        $this->assignPermissionsToRoles();
    }

    private function assignPermissionsToRoles()
    {
        $this->command->info('Assigning permissions to roles...');

        $student = Role::where('name', 'student')->first();
        $instructor = Role::where('name', 'instructor')->first();
        $admin = Role::where('name', 'admin')->first();
        $superadmin = Role::where('name', 'superadmin')->first();

        // Student permissions
        $student->permissions()->sync(
            Permission::whereIn('name', ['courses.view'])->pluck('id')
        );

        // Instructor permissions
        $instructor->permissions()->sync(
            Permission::whereIn('name', [
                'courses.view', 'courses.create', 'courses.edit', 'courses.publish',
                'payments.view'
            ])->pluck('id')
        );

        // Admin permissions
        $admin->permissions()->sync(
            Permission::whereIn('name', [
                'courses.view', 'courses.create', 'courses.edit', 'courses.delete', 'courses.publish',
                'users.view', 'users.create', 'users.edit',
                'payments.view', 'payments.manage'
            ])->pluck('id')
        );

        // Super admin permissions (all)
        $superadmin->permissions()->sync(Permission::all()->pluck('id'));
    }

    private function createCourseCategories()
    {
        $this->command->info('Creating course categories...');

        $categories = [
            [
                'name' => 'AI & Tech',
                'slug' => 'ai-tech',
                'description' => 'Artificial Intelligence and Technology courses',
                'icon' => 'fas fa-robot',
                'color' => '#8B5CF6',
                'is_featured' => true,
                'subcategories' => [
                    'Machine Learning',
                    'Deep Learning',
                    'Natural Language Processing',
                    'Computer Vision',
                    'AI Ethics'
                ]
            ],
            [
                'name' => 'Business',
                'slug' => 'business',
                'description' => 'Business and entrepreneurship courses',
                'icon' => 'fas fa-briefcase',
                'color' => '#10B981',
                'is_featured' => true,
                'subcategories' => [
                    'Entrepreneurship',
                    'Management',
                    'Strategy',
                    'Operations',
                    'Business Analytics'
                ]
            ],
            [
                'name' => 'Mindset',
                'slug' => 'mindset',
                'description' => 'Personal development and mindset courses',
                'icon' => 'fas fa-brain',
                'color' => '#F59E0B',
                'is_featured' => true,
                'subcategories' => [
                    'Growth Mindset',
                    'Productivity',
                    'Goal Setting',
                    'Mental Health',
                    'Life Coaching'
                ]
            ],
            [
                'name' => 'Programming',
                'slug' => 'programming',
                'description' => 'Programming and software development courses',
                'icon' => 'fas fa-code',
                'color' => '#3B82F6',
                'is_featured' => true,
                'subcategories' => [
                    'Web Development',
                    'Mobile Development',
                    'Backend Development',
                    'Frontend Development',
                    'DevOps'
                ]
            ],
            [
                'name' => 'Fitness',
                'slug' => 'fitness',
                'description' => 'Health and fitness courses',
                'icon' => 'fas fa-dumbbell',
                'color' => '#EF4444',
                'is_featured' => false,
                'subcategories' => [
                    'Weight Training',
                    'Cardio',
                    'Yoga',
                    'Nutrition',
                    'Sports Training'
                ]
            ],
            [
                'name' => 'Finance',
                'slug' => 'finance',
                'description' => 'Finance and investment courses',
                'icon' => 'fas fa-chart-line',
                'color' => '#059669',
                'is_featured' => true,
                'subcategories' => [
                    'Personal Finance',
                    'Investing',
                    'Cryptocurrency',
                    'Trading',
                    'Financial Planning'
                ]
            ],
            [
                'name' => 'Leadership',
                'slug' => 'leadership',
                'description' => 'Leadership and management courses',
                'icon' => 'fas fa-users',
                'color' => '#7C3AED',
                'is_featured' => false,
                'subcategories' => [
                    'Team Management',
                    'Executive Leadership',
                    'Communication',
                    'Conflict Resolution',
                    'Strategic Planning'
                ]
            ],
            [
                'name' => 'Marketing',
                'slug' => 'marketing',
                'description' => 'Digital marketing and advertising courses',
                'icon' => 'fas fa-bullhorn',
                'color' => '#DC2626',
                'is_featured' => true,
                'subcategories' => [
                    'Digital Marketing',
                    'Social Media Marketing',
                    'Content Marketing',
                    'Email Marketing',
                    'SEO'
                ]
            ],
            [
                'name' => 'Automation',
                'slug' => 'automation',
                'description' => 'Business automation and workflow courses',
                'icon' => 'fas fa-cogs',
                'color' => '#6B7280',
                'is_featured' => false,
                'subcategories' => [
                    'Process Automation',
                    'Workflow Design',
                    'RPA',
                    'Integration',
                    'Efficiency'
                ]
            ],
            [
                'name' => 'Chatbot WA',
                'slug' => 'chatbot-wa',
                'description' => 'WhatsApp chatbot and messaging automation courses',
                'icon' => 'fab fa-whatsapp',
                'color' => '#25D366',
                'is_featured' => false,
                'subcategories' => [
                    'WhatsApp Business',
                    'Bot Development',
                    'Message Automation',
                    'Customer Service',
                    'API Integration'
                ]
            ],
            [
                'name' => 'Game',
                'slug' => 'game',
                'description' => 'Game development and design courses',
                'icon' => 'fas fa-gamepad',
                'color' => '#8B5CF6',
                'is_featured' => false,
                'subcategories' => [
                    'Game Design',
                    'Unity Development',
                    'Unreal Engine',
                    'Mobile Games',
                    '2D/3D Graphics'
                ]
            ],
            [
                'name' => 'Cybersecurity',
                'slug' => 'cybersecurity',
                'description' => 'Cybersecurity and information security courses',
                'icon' => 'fas fa-shield-alt',
                'color' => '#DC2626',
                'is_featured' => false,
                'subcategories' => [
                    'Ethical Hacking',
                    'Network Security',
                    'Penetration Testing',
                    'Security Auditing',
                    'Incident Response'
                ]
            ],
            [
                'name' => 'Cloud Computing',
                'slug' => 'cloud-computing',
                'description' => 'Cloud computing and infrastructure courses',
                'icon' => 'fas fa-cloud',
                'color' => '#0EA5E9',
                'is_featured' => false,
                'subcategories' => [
                    'AWS',
                    'Azure',
                    'Google Cloud',
                    'Docker',
                    'Kubernetes'
                ]
            ],
            [
                'name' => 'Data Science',
                'slug' => 'data-science',
                'description' => 'Data science and analytics courses',
                'icon' => 'fas fa-chart-bar',
                'color' => '#059669',
                'is_featured' => true,
                'subcategories' => [
                    'Data Analysis',
                    'Statistics',
                    'Python for Data Science',
                    'R Programming',
                    'Big Data'
                ]
            ],
            [
                'name' => 'Internet of Things (IoT)',
                'slug' => 'iot',
                'description' => 'Internet of Things and connected devices courses',
                'icon' => 'fas fa-wifi',
                'color' => '#6366F1',
                'is_featured' => false,
                'subcategories' => [
                    'Arduino',
                    'Raspberry Pi',
                    'Sensors',
                    'Smart Home',
                    'Industrial IoT'
                ]
            ],
            [
                'name' => 'Blockchain',
                'slug' => 'blockchain',
                'description' => 'Blockchain and cryptocurrency development courses',
                'icon' => 'fas fa-link',
                'color' => '#F59E0B',
                'is_featured' => false,
                'subcategories' => [
                    'Smart Contracts',
                    'DeFi',
                    'NFTs',
                    'Ethereum',
                    'Web3'
                ]
            ],
            [
                'name' => 'Virtual Reality (VR)',
                'slug' => 'virtual-reality',
                'description' => 'Virtual Reality development and design courses',
                'icon' => 'fas fa-vr-cardboard',
                'color' => '#8B5CF6',
                'is_featured' => false,
                'subcategories' => [
                    'VR Development',
                    '3D Modeling',
                    'Unity VR',
                    'Oculus',
                    'VR Design'
                ]
            ],
            [
                'name' => 'Augmented Reality (AR)',
                'slug' => 'augmented-reality',
                'description' => 'Augmented Reality development and applications courses',
                'icon' => 'fas fa-eye',
                'color' => '#10B981',
                'is_featured' => false,
                'subcategories' => [
                    'AR Development',
                    'ARKit',
                    'ARCore',
                    'Mixed Reality',
                    'AR Design'
                ]
            ],
            [
                'name' => 'Robotics',
                'slug' => 'robotics',
                'description' => 'Robotics and automation courses',
                'icon' => 'fas fa-robot',
                'color' => '#6B7280',
                'is_featured' => false,
                'subcategories' => [
                    'Robot Programming',
                    'Mechanical Design',
                    'Control Systems',
                    'AI Robotics',
                    'Industrial Robotics'
                ]
            ],
            [
                'name' => 'Quantum Computing',
                'slug' => 'quantum-computing',
                'description' => 'Quantum computing and quantum algorithms courses',
                'icon' => 'fas fa-atom',
                'color' => '#7C3AED',
                'is_featured' => false,
                'subcategories' => [
                    'Quantum Algorithms',
                    'Quantum Programming',
                    'Quantum Physics',
                    'Quantum Cryptography',
                    'Quantum Hardware'
                ]
            ]
        ];

        foreach ($categories as $categoryData) {
            $subcategories = $categoryData['subcategories'];
            unset($categoryData['subcategories']);

            $category = CourseCategory::firstOrCreate(
                ['slug' => $categoryData['slug']],
                $categoryData
            );

            // Create subcategories
            foreach ($subcategories as $subName) {
                CourseCategory::firstOrCreate(
                    ['slug' => Str::slug($subName), 'parent_id' => $category->id],
                    [
                        'name' => $subName,
                        'slug' => Str::slug($subName),
                        'description' => $subName . ' courses',
                        'parent_id' => $category->id,
                        'is_active' => true
                    ]
                );
            }
        }
    }

    private function createTestUsers()
    {
        $this->command->info('Creating test users with secure passwords...');

        // Generate secure passwords
        $superadminPassword = $this->generateSecurePassword();
        $adminPassword = $this->generateSecurePassword();
        $instructorPassword = $this->generateSecurePassword();
        $studentPassword = $this->generateSecurePassword();

        // Create super admin
        $superadmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make($superadminPassword),
                'email_verified_at' => now(),
                'role' => 'superadmin'
            ]
        );
        $superadmin->roles()->sync([Role::where('name', 'superadmin')->first()->id]);

        // Create admin
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make($adminPassword),
                'email_verified_at' => now(),
                'role' => 'admin'
            ]
        );
        $admin->roles()->sync([Role::where('name', 'admin')->first()->id]);

        // Create instructor
        $instructor = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'John Instructor',
                'email' => '<EMAIL>',
                'password' => Hash::make($instructorPassword),
                'email_verified_at' => now(),
                'role' => 'instructor',
                'bio' => 'Experienced web developer and instructor with 10+ years in the industry.'
            ]
        );
        $instructor->roles()->sync([Role::where('name', 'instructor')->first()->id]);

        // Create student
        $student = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Jane Student',
                'email' => '<EMAIL>',
                'password' => Hash::make($studentPassword),
                'email_verified_at' => now(),
                'role' => 'student'
            ]
        );
        $student->roles()->sync([Role::where('name', 'student')->first()->id]);

        $this->command->info('Test users created with secure passwords:');
        $this->command->info('- <EMAIL> (password: ' . $superadminPassword . ')');
        $this->command->info('- <EMAIL> (password: ' . $adminPassword . ')');
        $this->command->info('- <EMAIL> (password: ' . $instructorPassword . ')');
        $this->command->info('- <EMAIL> (password: ' . $studentPassword . ')');
    }

    private function createAdditionalStudents()
    {
        $this->command->info('Creating additional student accounts with secure passwords...');

        $students = [
            [
                'name' => 'Alice Johnson',
                'email' => '<EMAIL>',
                'bio' => 'Marketing professional looking to learn web development'
            ],
            [
                'name' => 'Bob Wilson',
                'email' => '<EMAIL>',
                'bio' => 'Recent graduate interested in full-stack development'
            ],
            [
                'name' => 'Carol Davis',
                'email' => '<EMAIL>',
                'bio' => 'Career changer transitioning from finance to tech'
            ],
            [
                'name' => 'David Brown',
                'email' => '<EMAIL>',
                'bio' => 'Entrepreneur building his own startup'
            ],
            [
                'name' => 'Emma Garcia',
                'email' => '<EMAIL>',
                'bio' => 'Designer wanting to learn coding skills'
            ]
        ];

        $this->command->info('Additional student passwords:');
        foreach ($students as $studentData) {
            $securePassword = $this->generateSecurePassword();
            $student = User::firstOrCreate(
                ['email' => $studentData['email']],
                array_merge($studentData, [
                    'password' => Hash::make($securePassword),
                    'email_verified_at' => now(),
                    'role' => 'student'
                ])
            );
            $student->roles()->sync([Role::where('name', 'student')->first()->id]);
            $this->command->info('- ' . $studentData['email'] . ' (password: ' . $securePassword . ')');
        }

        $this->command->info('Additional student accounts created.');
    }

    private function createSampleEnrollments()
    {
        $this->command->info('Creating sample enrollments and payments...');

        // Get all students and courses
        $students = User::where('role', 'student')->get();
        $courses = \App\Models\Course::where('status', 'published')->get();

        if ($students->isEmpty() || $courses->isEmpty()) {
            $this->command->warn('No students or courses found. Skipping enrollment creation.');
            return;
        }

        // Create enrollments for each student
        foreach ($students as $student) {
            // Each student enrolls in 1-3 random courses
            $coursesToEnroll = $courses->random(rand(1, min(3, $courses->count())));

            foreach ($coursesToEnroll as $course) {
                // Create payment record
                $paidAt = now()->subDays(rand(1, 30));
                $payment = \App\Models\Payment::create([
                    'user_id' => $student->id,
                    'course_id' => $course->id,
                    'instructor_id' => $course->instructor_id,
                    'amount' => $course->price,
                    'instructor_amount' => $course->price * 0.85, // 85% to instructor, 15% platform fee
                    'platform_fee' => $course->price * 0.15,
                    'processing_fee' => $course->price * 0.03, // 3% processing fee
                    'currency' => $course->currency,
                    'payment_method' => 'paypal',
                    'status' => 'completed',
                    'transaction_id' => 'SAMPLE_' . strtoupper(Str::random(10)),
                    'payment_provider_id' => 'ORDER_' . strtoupper(Str::random(15)),
                    'payment_details' => [
                        'payer_email' => $student->email,
                        'payer_name' => $student->name,
                        'payment_date' => $paidAt->toISOString()
                    ],
                    'paid_at' => $paidAt
                ]);

                // Create enrollment
                $completedLectureIds = $this->generateCompletedLectures($course);
                $enrollment = \App\Models\Enrollment::create([
                    'user_id' => $student->id,
                    'course_id' => $course->id,
                    'instructor_id' => $course->instructor_id,
                    'enrolled_at' => $payment->paid_at,
                    'status' => 'active',
                    'progress_percentage' => rand(0, 100),
                    'completed_lectures' => count($completedLectureIds),
                    'total_lectures' => $course->lectures()->count(),
                    'last_accessed_at' => now()->subDays(rand(0, 7)),
                    'completed_lecture_ids' => $completedLectureIds,
                    'current_lecture_id' => $this->getCurrentLecture($course)
                ]);

                // Update course statistics
                $course->increment('total_students');
            }
        }

        // Update all course statistics
        foreach ($courses as $course) {
            $course->updateStatistics();
        }

        $this->command->info('Sample enrollments and payments created.');
    }

    private function generateCompletedLectures(\App\Models\Course $course)
    {
        $lectures = $course->lectures()->pluck('lectures.id')->toArray();
        if (empty($lectures)) {
            return [];
        }

        // Randomly complete 0-80% of lectures
        $completionRate = rand(0, 80) / 100;
        $lectureCount = count($lectures);
        $completedCount = (int) ($lectureCount * $completionRate);

        return array_slice($lectures, 0, $completedCount);
    }

    private function getCurrentLecture(\App\Models\Course $course)
    {
        $lectures = $course->lectures()->orderBy('lectures.sort_order')->pluck('lectures.id')->toArray();
        if (empty($lectures)) {
            return null;
        }

        // Return a random lecture as current
        return $lectures[rand(0, count($lectures) - 1)];
    }

    /**
     * Migrate existing courses from old string-based categories to new category system
     */
    private function migrateCourseCategories()
    {
        $this->command->info('Migrating existing courses to new category system...');

        // Get all courses that still use old string-based categories
        $courses = \App\Models\Course::whereNull('category_id')->get();

        if ($courses->isEmpty()) {
            $this->command->info('No courses need category migration.');
            return;
        }

        // Get all available categories
        $categories = CourseCategory::whereNull('parent_id')->get();

        // Create a mapping of old category names to new category IDs
        $categoryMapping = [
            'general' => 'business',
            'programming' => 'programming',
            'business' => 'business',
            'design' => 'design-creativity',
            'marketing' => 'marketing',
            'technology' => 'ai-tech',
            'ai' => 'ai-tech',
            'tech' => 'ai-tech',
            'development' => 'programming',
            'web development' => 'programming',
            'data science' => 'data-science',
            'finance' => 'finance-accounting',
            'health' => 'health-fitness',
            'fitness' => 'health-fitness',
            'music' => 'music',
            'photography' => 'photography-video',
            'video' => 'photography-video',
            'lifestyle' => 'lifestyle',
            'personal development' => 'personal-development',
            'teaching' => 'teaching-academics'
        ];

        foreach ($courses as $course) {
            $oldCategory = strtolower(trim($course->category));

            // Find matching category
            $newCategorySlug = $categoryMapping[$oldCategory] ?? 'business'; // Default to business
            $newCategory = $categories->firstWhere('slug', $newCategorySlug);

            if ($newCategory) {
                $course->update([
                    'category_id' => $newCategory->id,
                    'category' => $newCategory->name // Update the old field too for backward compatibility
                ]);

                $this->command->info("✅ Migrated course '{$course->title}' from '{$oldCategory}' to '{$newCategory->name}'");
            } else {
                // Fallback to first available category
                $fallbackCategory = $categories->first();
                if ($fallbackCategory) {
                    $course->update([
                        'category_id' => $fallbackCategory->id,
                        'category' => $fallbackCategory->name
                    ]);
                    $this->command->info("⚠️  Migrated course '{$course->title}' to fallback category '{$fallbackCategory->name}'");
                }
            }
        }

        $this->command->info("✅ Course category migration completed! Migrated {$courses->count()} courses.");
    }
}
