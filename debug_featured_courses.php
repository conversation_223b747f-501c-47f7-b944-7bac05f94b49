<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Course;

echo "=== All Courses ===\n";
$allCourses = Course::select('id', 'title', 'featured', 'status')->get();
foreach ($allCourses as $course) {
    echo "ID: {$course->id}\n";
    echo "Title: {$course->title}\n";
    echo "Featured: " . ($course->featured ? 'YES' : 'NO') . "\n";
    echo "Status: {$course->status}\n";
    echo "---\n";
}

echo "\n=== Featured Courses ===\n";
$featuredCourses = Course::where('featured', true)->select('id', 'title', 'featured', 'status')->get();
if ($featuredCourses->count() > 0) {
    foreach ($featuredCourses as $course) {
        echo "ID: {$course->id}\n";
        echo "Title: {$course->title}\n";
        echo "Featured: " . ($course->featured ? 'YES' : 'NO') . "\n";
        echo "Status: {$course->status}\n";
        echo "---\n";
    }
} else {
    echo "No featured courses found!\n";
}

echo "\n=== Featured AND Published Courses ===\n";
$featuredPublishedCourses = Course::where('featured', true)
    ->where('status', 'published')
    ->select('id', 'title', 'featured', 'status')
    ->get();
    
if ($featuredPublishedCourses->count() > 0) {
    foreach ($featuredPublishedCourses as $course) {
        echo "ID: {$course->id}\n";
        echo "Title: {$course->title}\n";
        echo "Featured: " . ($course->featured ? 'YES' : 'NO') . "\n";
        echo "Status: {$course->status}\n";
        echo "---\n";
    }
} else {
    echo "No featured AND published courses found! This is why the Featured Transformations section is empty.\n";
}

echo "\n=== Published Courses (not featured) ===\n";
$publishedNotFeatured = Course::where('status', 'published')
    ->where('featured', false)
    ->select('id', 'title', 'featured', 'status')
    ->get();
    
if ($publishedNotFeatured->count() > 0) {
    foreach ($publishedNotFeatured as $course) {
        echo "ID: {$course->id}\n";
        echo "Title: {$course->title}\n";
        echo "Featured: " . ($course->featured ? 'YES' : 'NO') . "\n";
        echo "Status: {$course->status}\n";
        echo "---\n";
    }
} else {
    echo "No published courses that are not featured.\n";
}