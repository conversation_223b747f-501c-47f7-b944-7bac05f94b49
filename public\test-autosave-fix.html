<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto-save Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #333; border-radius: 8px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .status.saving { background: #1e40af; color: white; }
        .status.saved { background: #059669; color: white; }
        .status.error { background: #dc2626; color: white; }
        .hidden { display: none; }
        input, textarea { padding: 8px; margin: 5px 0; width: 100%; background: #333; color: white; border: 1px solid #555; }
        button { padding: 10px 20px; background: #dc2626; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #b91c1c; }
        .log { background: #111; padding: 10px; margin: 10px 0; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Course Builder Auto-save Fix Test</h1>
        
        <!-- Save Status Indicator -->
        <div id="save-status-indicator" class="status hidden">
            <span id="save-status-text">Ready</span>
        </div>
        
        <div class="test-section">
            <h2>Test 1: showSaveStatus Function</h2>
            <p>Testing the fixed showSaveStatus function to ensure no infinite recursion.</p>
            <button onclick="testShowSaveStatus()">Test Save Status</button>
            <div id="test1-log" class="log"></div>
        </div>
        
        <div class="test-section">
            <h2>Test 2: Auto-save Scheduling</h2>
            <p>Testing the scheduleAutoSave function with mock save operations.</p>
            <form id="test-form">
                <input type="text" id="test-title" placeholder="Course Title" value="Test Course">
                <textarea id="test-description" placeholder="Course Description">Test Description</textarea>
                <textarea id="test-what-learn" placeholder="What You'll Learn">Test learning objectives</textarea>
            </form>
            <button onclick="testAutoSave()">Test Auto-save</button>
            <div id="test2-log" class="log"></div>
        </div>
        
        <div class="test-section">
            <h2>Test 3: Multiple Function Calls</h2>
            <p>Testing rapid successive calls to ensure no stack overflow.</p>
            <button onclick="testRapidCalls()">Test Rapid Calls</button>
            <div id="test3-log" class="log"></div>
        </div>
    </div>

    <script>
        // Mock global variables
        let autoSaveTimeouts = new Map();
        let courseId = 'test-course-id';
        
        // Mock utility functions (from course-builder-utils.js)
        function showSaveStatus(status, message) {
            const indicator = document.getElementById('save-status-indicator');
            const statusText = document.getElementById('save-status-text');

            if (!indicator || !statusText) {
                console.warn('Save status elements not found');
                return;
            }

            // Remove existing status classes
            indicator.className = indicator.className.replace(/bg-\w+-\d+/g, '').replace(/text-\w+-\d+/g, '');

            switch (status) {
                case 'saving':
                    indicator.className = 'status saving';
                    statusText.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>' + message;
                    break;
                case 'saved':
                    indicator.className = 'status saved';
                    statusText.innerHTML = '<i class="fas fa-check mr-2"></i>' + message;
                    break;
                case 'error':
                    indicator.className = 'status error';
                    statusText.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>' + message;
                    break;
            }

            indicator.classList.remove('hidden');

            // Auto-hide success/error messages
            if (status !== 'saving') {
                setTimeout(() => {
                    indicator.classList.add('hidden');
                }, 3000);
            }
        }

        function showError(message) {
            showSaveStatus('error', message);
        }

        // Fixed scheduleAutoSave function (from course-builder-autosave.js)
        function scheduleAutoSave(type, id, saveFunction) {
            const key = `${type}-${id}`;

            console.log('Scheduling auto-save for:', key);

            // Clear existing timeout
            if (autoSaveTimeouts.has(key)) {
                clearTimeout(autoSaveTimeouts.get(key));
                console.log('Cleared existing timeout for:', key);
            }

            // Show saving indicator using the utility function
            if (typeof showSaveStatus === 'function') {
                showSaveStatus('saving', 'Saving...');
            } else {
                console.warn('showSaveStatus function not available');
            }

            // Schedule new save
            const timeoutId = setTimeout(() => {
                console.log('Executing auto-save for:', key);
                try {
                    saveFunction();
                } catch (error) {
                    console.error('Error in save function:', error);
                    if (typeof showError === 'function') {
                        showError('Save function error: ' + error.message);
                    } else {
                        console.error('showError function not available');
                    }
                }
                autoSaveTimeouts.delete(key);
            }, 1000); // 1 second delay

            autoSaveTimeouts.set(key, timeoutId);
        }

        // Mock save function
        function mockSaveCourse() {
            console.log('Mock save course executed');
            setTimeout(() => {
                showSaveStatus('saved', 'Course saved successfully!');
            }, 500);
        }

        // Test functions
        function testShowSaveStatus() {
            const log = document.getElementById('test1-log');
            log.innerHTML = '';
            
            try {
                log.innerHTML += 'Testing showSaveStatus function...\n';
                
                showSaveStatus('saving', 'Testing save status...');
                log.innerHTML += '✓ showSaveStatus(saving) - OK\n';
                
                setTimeout(() => {
                    showSaveStatus('saved', 'Test completed successfully!');
                    log.innerHTML += '✓ showSaveStatus(saved) - OK\n';
                }, 1000);
                
                setTimeout(() => {
                    showSaveStatus('error', 'Test error message');
                    log.innerHTML += '✓ showSaveStatus(error) - OK\n';
                }, 2000);
                
            } catch (error) {
                log.innerHTML += '✗ Error: ' + error.message + '\n';
                log.innerHTML += '✗ Stack: ' + error.stack + '\n';
            }
        }

        function testAutoSave() {
            const log = document.getElementById('test2-log');
            log.innerHTML = '';
            
            try {
                log.innerHTML += 'Testing scheduleAutoSave function...\n';
                
                scheduleAutoSave('course', courseId, mockSaveCourse);
                log.innerHTML += '✓ scheduleAutoSave called - OK\n';
                
                setTimeout(() => {
                    log.innerHTML += '✓ Auto-save completed - OK\n';
                }, 2000);
                
            } catch (error) {
                log.innerHTML += '✗ Error: ' + error.message + '\n';
                log.innerHTML += '✗ Stack: ' + error.stack + '\n';
            }
        }

        function testRapidCalls() {
            const log = document.getElementById('test3-log');
            log.innerHTML = '';
            
            try {
                log.innerHTML += 'Testing rapid successive calls...\n';
                
                for (let i = 0; i < 10; i++) {
                    setTimeout(() => {
                        scheduleAutoSave('course', courseId + '-' + i, mockSaveCourse);
                        log.innerHTML += `✓ Call ${i + 1} - OK\n`;
                    }, i * 100);
                }
                
                setTimeout(() => {
                    log.innerHTML += '✓ All rapid calls completed - OK\n';
                }, 2000);
                
            } catch (error) {
                log.innerHTML += '✗ Error: ' + error.message + '\n';
                log.innerHTML += '✗ Stack: ' + error.stack + '\n';
            }
        }

        // Initialize test
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Auto-save fix test page loaded');
        });
    </script>
</body>
</html>
