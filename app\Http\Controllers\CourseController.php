<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\CourseCategory;
use Illuminate\Http\Request;

class CourseController extends Controller
{
    public function index(Request $request)
    {
        $query = Course::where('status', 'published')
            ->with(['instructor', 'category', 'subcategory']);

        // Filter by category (using new category system)
        if ($request->has('category') && $request->category !== 'all') {
            // Filter by category slug
            $query->whereHas('category', function($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        // Filter by level
        if ($request->has('level') && $request->level !== 'all') {
            $query->where('level', $request->level);
        }

        // Filter by price range
        if ($request->has('price_range') && $request->price_range !== 'all') {
            switch ($request->price_range) {
                case 'free':
                    $query->where('price', 0);
                    break;
                case 'under_50':
                    $query->where('price', '>', 0)->where('price', '<=', 50);
                    break;
                case '50_100':
                    $query->where('price', '>', 50)->where('price', '<=', 100);
                    break;
                case 'over_100':
                    $query->where('price', '>', 100);
                    break;
            }
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $query->where(function($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%')
                  ->orWhere('subtitle', 'like', '%' . $request->search . '%');
            });
        }

        // Sorting
        $sortBy = $request->get('sort', 'newest');
        switch ($sortBy) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'rating':
                $query->orderBy('average_rating', 'desc');
                break;
            case 'popular':
                $query->orderBy('total_students', 'desc');
                break;
            case 'newest':
            default:
                $query->orderBy('published_at', 'desc');
                break;
        }

        $courses = $query->paginate(12)->appends($request->query());

        // Get filter options - only categories that have published courses
        $categories = CourseCategory::whereNull('parent_id')
            ->whereHas('courses', function($query) {
                $query->where('status', 'published');
            })
            ->orderBy('name')
            ->get(['name', 'slug']);
        $levels = ['beginner', 'intermediate', 'advanced', 'all_levels'];
        $priceRanges = [
            'free' => 'Free',
            'under_50' => 'Under $50',
            '50_100' => '$50 - $100',
            'over_100' => 'Over $100'
        ];

        // Add enrollment status for authenticated users
        if (auth()->check()) {
            $userEnrollments = auth()->user()->enrollments()->pluck('course_id')->toArray();
            $courses->getCollection()->each(function ($course) use ($userEnrollments) {
                $course->is_enrolled = in_array($course->id, $userEnrollments);
            });
        } else {
            $courses->getCollection()->each(function ($course) {
                $course->is_enrolled = false;
            });
        }

        return view('courses.index', compact('courses', 'categories', 'levels', 'priceRanges'));
    }
    
    public function show(Course $course)
    {
        if ($course->status !== 'published') {
            abort(404);
        }

        // Load course with hierarchical structure - only published chapters for public view
        $course->load([
            'instructor',
            'category',
            'subcategory',
            'chapters' => function($query) {
                $query->where('is_published', true)->orderBy('sort_order');
            },
            'chapters.lectures' => function($query) {
                $query->where('is_published', true)->orderBy('sort_order');
            }
        ]);

        $relatedCourses = Course::where('category_id', $course->category_id)
            ->where('id', '!=', $course->id)
            ->where('status', 'published')
            ->with(['instructor', 'category'])
            ->take(3)
            ->get();

        $isEnrolled = auth()->check() && auth()->user()->enrollments()->where('course_id', $course->id)->exists();

        // Get user's progress if enrolled
        $userProgress = null;
        $completedLectureIds = [];
        if ($isEnrolled && auth()->check()) {
            $enrollment = auth()->user()->enrollments()->where('course_id', $course->id)->first();
            if ($enrollment) {
                $userProgress = $enrollment;
                $completedLectureIds = $enrollment->completed_lecture_ids ?? [];
            }
        }

        // Calculate course statistics
        $totalLectures = $course->lectures()->count();
        $totalDuration = $course->lectures()->sum('duration_minutes');
        $completedLectures = is_array($completedLectureIds) ? count($completedLectureIds) : 0;
        $progressPercentage = $totalLectures > 0 ? round(($completedLectures / $totalLectures) * 100) : 0;

        return view('courses.show', compact(
            'course',
            'relatedCourses',
            'isEnrolled',
            'userProgress',
            'completedLectureIds',
            'totalLectures',
            'totalDuration',
            'progressPercentage'
        ));
    }
    
    public function category($categorySlug)
    {
        // Find the category by slug
        $category = CourseCategory::where('slug', $categorySlug)->firstOrFail();

        $courses = Course::where('category_id', $category->id)
            ->where('status', 'published')
            ->with(['instructor', 'category'])
            ->paginate(12);

        return view('courses.category', compact('courses', 'category'));
    }
    


}
