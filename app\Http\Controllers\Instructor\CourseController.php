<?php

namespace App\Http\Controllers\Instructor;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\CourseCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class CourseController extends Controller
{
    /**
     * Display a listing of the instructor's courses.
     */
    public function index(Request $request)
    {
        $instructor = auth()->user();
        
        $query = $instructor->courses()
            ->with(['category', 'subcategory'])
            ->withCount(['enrollments', 'chapters', 'lectures', 'reviews']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        if ($request->filled('level')) {
            $query->where('level', $request->level);
        }

        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('subtitle', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'updated_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        
        $allowedSorts = ['title', 'created_at', 'updated_at', 'status', 'enrollments_count', 'average_rating'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortDirection);
        }

        $courses = $query->paginate(12)->withQueryString();

        // Get filter options
        $categories = CourseCategory::active()->parents()->orderBy('name')->get();
        $statuses = ['draft', 'under_review', 'published', 'archived'];
        $levels = ['beginner', 'intermediate', 'advanced', 'all_levels'];

        return view('instructor.courses.index', compact(
            'courses', 
            'categories', 
            'statuses', 
            'levels'
        ));
    }

    /**
     * Show the form for creating a new course.
     */
    public function create()
    {
        $categories = CourseCategory::active()->parents()->with('activeChildren')->orderBy('name')->get();
        $levels = ['beginner', 'intermediate', 'advanced', 'all_levels'];
        $languages = ['English', 'Spanish', 'French', 'German', 'Portuguese', 'Italian', 'Chinese', 'Japanese'];

        return view('instructor.courses.create', compact('categories', 'levels', 'languages'));
    }

    /**
     * Create a new course and redirect to course builder.
     */
    public function createAndBuild(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
        ]);

        // Get the default "General" category
        $generalCategory = CourseCategory::where('name', 'General')->first();

        // Create a basic course with minimal required data
        $course = Course::create([
            'title' => $request->title,
            'subtitle' => '',
            'description' => 'Course description will be added in the course builder.',
            'instructor_id' => auth()->id(),
            'slug' => $this->generateUniqueSlug($request->title),
            'status' => 'draft',
            'level' => 'beginner',
            'language' => 'English',
            'price' => 0,
            'category' => $generalCategory ? $generalCategory->name : 'General',
            'category_id' => $generalCategory ? $generalCategory->id : null,
            'what_you_will_learn' => [],
            'requirements' => [],
            'target_audience' => [],
        ]);

        return redirect()->route('instructor.course-builder.show', $course)
            ->with('success', 'Course created! Complete your course details using the course builder.');
    }

    /**
     * Store a newly created course in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'required|string|max:5000',
            'what_you_will_learn' => 'nullable|array',
            'what_you_will_learn.*' => 'string|max:255',
            'requirements' => 'nullable|array',
            'requirements.*' => 'string|max:255',
            'target_audience' => 'nullable|array',
            'target_audience.*' => 'string|max:255',
            'category_id' => 'required|exists:course_categories,id',
            'subcategory_id' => 'nullable|exists:course_categories,id',
            'language' => 'required|string|max:50',
            'level' => 'required|in:beginner,intermediate,advanced,all_levels',
            'price' => 'required|numeric|min:0|max:999.99',
            'original_price' => 'nullable|numeric|min:0|max:999.99',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'promotional_video' => 'nullable|url',
        ]);

        $data = $request->all();
        $data['instructor_id'] = auth()->id();
        $data['slug'] = $this->generateUniqueSlug($request->title);
        $data['status'] = 'draft';

        // Handle category names for backward compatibility
        if ($request->category_id) {
            $category = CourseCategory::find($request->category_id);
            $data['category'] = $category ? $category->name : null;
        }
        if ($request->subcategory_id) {
            $subcategory = CourseCategory::find($request->subcategory_id);
            $data['subcategory'] = $subcategory ? $subcategory->name : null;
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            $data['image'] = $this->storeImage($request->file('image'), auth()->id());
        }

        $course = Course::create($data);

        return redirect()->route('instructor.courses.show', $course)
            ->with('success', 'Course created successfully! You can now add chapters and lectures.');
    }

    /**
     * Display the specified course.
     */
    public function show(Course $course)
    {
        $this->authorize('view', $course);

        // Redirect to the new course builder interface for unified course editing experience
        return redirect()->route('instructor.course-builder.show', $course)
            ->with('info', 'Redirected to the new course builder interface for better course management.');
    }

    /**
     * Show the form for editing the specified course.
     */
    public function edit(Course $course)
    {
        $this->authorize('update', $course);

        $categories = CourseCategory::active()->parents()->with('activeChildren')->orderBy('name')->get();
        $levels = ['beginner', 'intermediate', 'advanced', 'all_levels'];
        $languages = ['English', 'Spanish', 'French', 'German', 'Portuguese', 'Italian', 'Chinese', 'Japanese'];

        return view('instructor.courses.edit', compact('course', 'categories', 'levels', 'languages'));
    }

    /**
     * Update the specified course in storage.
     */
    public function update(Request $request, Course $course)
    {
        $this->authorize('update', $course);

        $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'required|string|max:5000',
            'what_you_will_learn' => 'nullable|array',
            'what_you_will_learn.*' => 'string|max:255',
            'requirements' => 'nullable|array',
            'requirements.*' => 'string|max:255',
            'target_audience' => 'nullable|array',
            'target_audience.*' => 'string|max:255',
            'category_id' => 'required|exists:course_categories,id',
            'subcategory_id' => 'nullable|exists:course_categories,id',
            'language' => 'required|string|max:50',
            'level' => 'required|in:beginner,intermediate,advanced,all_levels',
            'price' => 'required|numeric|min:0|max:999.99',
            'original_price' => 'nullable|numeric|min:0|max:999.99',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'promotional_video' => 'nullable|url',
        ]);

        $data = $request->all();

        // Update slug if title changed
        if ($request->title !== $course->title) {
            $data['slug'] = $this->generateUniqueSlug($request->title, $course->id);
        }

        // Handle category names for backward compatibility
        if ($request->category_id) {
            $category = CourseCategory::find($request->category_id);
            $data['category'] = $category ? $category->name : null;
        }
        if ($request->subcategory_id) {
            $subcategory = CourseCategory::find($request->subcategory_id);
            $data['subcategory'] = $subcategory ? $subcategory->name : null;
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($course->image) {
                Storage::disk('private')->delete($course->image);
            }
            $data['image'] = $this->storeImage($request->file('image'), auth()->id(), $course->id);
        }

        $course->update($data);

        return redirect()->route('instructor.courses.show', $course)
            ->with('success', 'Course updated successfully!');
    }

    /**
     * Update course basic details (Step 1 information).
     */
    public function updateDetails(Request $request, Course $course)
    {
        $this->authorize('update', $course);

        $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'required|string|max:5000',
            'category_id' => 'required|exists:course_categories,id',
            'subcategory_id' => 'nullable|exists:course_categories,id',
            'language' => 'required|string|max:50',
            'level' => 'required|in:beginner,intermediate,advanced,all_levels',
            'access_type' => 'required|in:free,paid',
            'price' => 'required_if:access_type,paid|nullable|numeric|min:0|max:999.99',
            'original_price' => 'nullable|numeric|min:0|max:999.99',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'promotional_video' => 'nullable|url',
        ]);

        $data = $request->all();

        // Handle pricing based on access type
        if ($request->access_type === 'free') {
            $data['price'] = 0;
            $data['original_price'] = null;
        } else {
            $data['price'] = $request->price ?? 0;
        }

        // Update slug if title changed
        if ($request->title !== $course->title) {
            $data['slug'] = $this->generateUniqueSlug($request->title, $course->id);
        }

        // Handle category names for backward compatibility
        if ($request->category_id) {
            $category = CourseCategory::find($request->category_id);
            $data['category'] = $category ? $category->name : null;
        }
        if ($request->subcategory_id) {
            $subcategory = CourseCategory::find($request->subcategory_id);
            $data['subcategory'] = $subcategory ? $subcategory->name : null;
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($course->image) {
                Storage::disk('private')->delete($course->image);
            }
            $data['image'] = $this->storeImage($request->file('image'), auth()->id(), $course->id);
        }

        $course->update($data);

        // Return JSON response for AJAX requests
        if ($request->ajax() || $request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Course details updated successfully!',
                'course' => $course->fresh()
            ]);
        }

        return redirect()->route('instructor.courses.show', $course)
            ->with('success', 'Course details updated successfully!');
    }

    /**
     * Remove the specified course from storage.
     */
    public function destroy(Course $course)
    {
        $this->authorize('delete', $course);

        // Check if course has enrollments
        if ($course->enrollments()->count() > 0) {
            return redirect()->route('instructor.courses.index')
                ->with('error', 'Cannot delete course with active enrollments. Archive it instead.');
        }

        // Delete course image
        if ($course->image) {
            Storage::disk('private')->delete($course->image);
        }

        $course->delete();

        return redirect()->route('instructor.courses.index')
            ->with('success', 'Course deleted successfully.');
    }

    /**
     * Toggle course status.
     */
    public function toggleStatus(Course $course)
    {
        $this->authorize('update', $course);

        $newStatus = $course->status === 'published' ? 'draft' : 'published';
        
        // Validate course is ready for publishing
        if ($newStatus === 'published') {
            $validation = $this->validateCourseForPublishing($course);
            if (!$validation['valid']) {
                return redirect()->back()->with('error', $validation['message']);
            }
            $course->update(['published_at' => now()]);
        }

        $course->update(['status' => $newStatus]);

        $message = $newStatus === 'published' ? 'Course published successfully!' : 'Course unpublished successfully.';

        return redirect()->back()->with('success', $message);
    }

    /**
     * Duplicate a course.
     */
    public function duplicate(Course $course)
    {
        $this->authorize('view', $course);

        $newCourse = $course->replicate();
        $newCourse->title = $course->title . ' (Copy)';
        $newCourse->slug = $this->generateUniqueSlug($newCourse->title);
        $newCourse->status = 'draft';
        $newCourse->featured = false;
        $newCourse->published_at = null;
        $newCourse->save();

        return redirect()->route('instructor.courses.edit', $newCourse)
            ->with('success', 'Course duplicated successfully! You can now edit the copy.');
    }

    /**
     * Generate a unique slug for the course.
     */
    private function generateUniqueSlug(string $title, ?string $excludeId = null): string
    {
        $slug = Str::slug($title);
        $originalSlug = $slug;
        $counter = 1;

        while (true) {
            $query = Course::where('slug', $slug);
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }

            if (!$query->exists()) {
                break;
            }

            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Store course image in private storage.
     */
    private function storeImage($file, $userId, $courseId = null): string
    {
        $directory = $courseId
            ? "courses/{$userId}/{$courseId}/images"
            : "courses/{$userId}/temp-images";

        $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
        return $file->storeAs($directory, $filename, 'private');
    }

    /**
     * Validate course is ready for publishing.
     */
    private function validateCourseForPublishing(Course $course): array
    {
        $errors = [];

        // Check required fields
        if (empty($course->title) || empty($course->description)) {
            $errors[] = 'Course must have title and description';
        }

        if (empty($course->image)) {
            $errors[] = 'Course must have a thumbnail image';
        }

        // Check course content
        if ($course->chapters()->count() === 0) {
            $errors[] = 'Course must have at least one chapter';
        }

        if ($course->lectures()->count() === 0) {
            $errors[] = 'Course must have at least one lecture';
        }

        // Check minimum content duration (e.g., 30 minutes)
        if ($course->total_duration_minutes < 30) {
            $errors[] = 'Course must have at least 30 minutes of content';
        }

        return [
            'valid' => empty($errors),
            'message' => empty($errors) ? 'Course is ready for publishing' : implode(', ', $errors)
        ];
    }
}
