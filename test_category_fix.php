<?php

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Course;
use App\Models\CourseCategory;
use App\Models\User;

echo "=== Testing Category Fix ===\n\n";

// Check categories
echo "Available categories:\n";
$categories = CourseCategory::where('parent_id', null)->get();
foreach ($categories as $category) {
    echo "- {$category->name} (ID: {$category->id}, Slug: {$category->slug})\n";
}

echo "\nCourses in database:\n";
$courses = Course::with(['category', 'subcategory'])->get();
foreach ($courses as $course) {
    $categoryName = $course->category ? $course->category->name : ($course->category ?? 'No category');
    echo "- {$course->title} -> Category: {$categoryName}\n";
}

// If no courses exist, create some test courses
if ($courses->isEmpty()) {
    echo "\nNo courses found. Creating test courses...\n";
    
    $instructor = User::where('email', '<EMAIL>')->first();
    if (!$instructor) {
        echo "No instructor found. Please run the seeder first.\n";
        exit;
    }
    
    $programmingCategory = $categories->where('slug', 'programming')->first();
    $businessCategory = $categories->where('slug', 'business')->first();
    $aiTechCategory = $categories->where('slug', 'ai-tech')->first();
    
    if (!$programmingCategory || !$businessCategory || !$aiTechCategory) {
        echo "Required categories not found. Please run the seeder first.\n";
        exit;
    }
    
    // Create test courses
    $testCourses = [
        [
            'title' => 'React.js Complete Guide',
            'subtitle' => 'Learn React from basics to advanced',
            'description' => 'A comprehensive guide to React.js development',
            'category_id' => $programmingCategory->id,
            'featured' => true,
            'status' => 'published',
            'price' => 99.99
        ],
        [
            'title' => 'Business Strategy Fundamentals',
            'subtitle' => 'Master business strategy and planning',
            'description' => 'Learn essential business strategy concepts',
            'category_id' => $businessCategory->id,
            'featured' => true,
            'status' => 'published',
            'price' => 79.99
        ],
        [
            'title' => 'AI and Machine Learning Basics',
            'subtitle' => 'Introduction to AI and ML concepts',
            'description' => 'Get started with artificial intelligence',
            'category_id' => $aiTechCategory->id,
            'featured' => true,
            'status' => 'published',
            'price' => 129.99
        ]
    ];
    
    foreach ($testCourses as $courseData) {
        $courseData['instructor_id'] = $instructor->id;
        $courseData['slug'] = \Illuminate\Support\Str::slug($courseData['title']);
        $courseData['level'] = 'beginner';
        $courseData['language'] = 'English';
        $courseData['what_you_will_learn'] = ['Learn the basics', 'Build practical projects'];
        $courseData['requirements'] = ['Basic computer skills'];
        $courseData['target_audience'] = ['Beginners', 'Students'];
        
        Course::create($courseData);
        echo "Created course: {$courseData['title']}\n";
    }
}

echo "\n=== Test Complete ===\n";
