<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';

// Get all courses
$courses = \App\Models\Course::select('id', 'title', 'featured', 'status')->get();

echo "Total courses: " . $courses->count() . "\n\n";

foreach($courses as $course) {
    echo "ID: {$course->id}\n";
    echo "Title: {$course->title}\n";
    echo "Featured: " . ($course->featured ? 'true' : 'false') . "\n";
    echo "Status: {$course->status}\n";
    echo "---\n";
}

// Check specifically for featured and published courses
$featuredCourses = \App\Models\Course::where('featured', true)
    ->where('status', 'published')
    ->select('id', 'title', 'featured', 'status')
    ->get();

echo "\nFeatured AND Published courses: " . $featuredCourses->count() . "\n";
foreach($featuredCourses as $course) {
    echo "- {$course->title} (ID: {$course->id})\n";
}