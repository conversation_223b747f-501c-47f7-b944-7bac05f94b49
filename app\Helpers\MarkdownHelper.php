<?php

namespace App\Helpers;

use League\CommonMark\CommonMarkConverter;
use League\CommonMark\Environment\Environment;
use League\CommonMark\Extension\CommonMark\CommonMarkCoreExtension;

class MarkdownHelper
{
    private static $converter = null;

    /**
     * Get the CommonMark converter instance
     */
    private static function getConverter(): CommonMarkConverter
    {
        if (self::$converter === null) {
            $config = [
                'html_input' => 'escape',
                'allow_unsafe_links' => false,
            ];

            $environment = new Environment($config);
            $environment->addExtension(new CommonMarkCoreExtension());

            self::$converter = new CommonMarkConverter($environment);
        }

        return self::$converter;
    }

    /**
     * Convert markdown text to HTML
     * 
     * @param string|null $text
     * @return string
     */
    public static function toHtml(?string $text): string
    {
        if (empty($text)) {
            return '';
        }

        return self::getConverter()->convert($text)->getContent();
    }

    /**
     * Convert simple markdown formatting (mainly **bold**) to HTML
     * This is a lighter version for basic formatting
     *
     * @param string|null $text
     * @return string
     */
    public static function toHtmlSimple(?string $text): string
    {
        if (empty($text)) {
            return '';
        }

        // First escape HTML to prevent XSS
        $text = htmlspecialchars($text, ENT_QUOTES, 'UTF-8');

        // Convert **bold** to <strong>bold</strong>
        $text = preg_replace('/\*\*(.*?)\*\*/', '<strong>$1</strong>', $text);

        // Convert *italic* to <em>italic</em> (but not if it's already part of **bold**)
        $text = preg_replace('/(?<!\*)\*([^*]+?)\*(?!\*)/', '<em>$1</em>', $text);

        // Convert line breaks to <br> tags
        $text = nl2br($text);

        return $text;
    }
}
