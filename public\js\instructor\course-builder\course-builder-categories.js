/**
 * Course Builder Category Management Module
 * Handles dynamic category creation and management
 */

// Category management state
let categoryFormVisible = false;

/**
 * Initialize category management functionality
 */
function initializeCategoryManagement() {
    console.log('Initializing category management...');
    
    // Add event listeners for both mobile and desktop add category buttons
    const addCategoryBtn = document.getElementById('add-category-btn');
    const addCategoryBtnDesktop = document.getElementById('add-category-btn-desktop');
    
    if (addCategoryBtn) {
        addCategoryBtn.addEventListener('click', showAddCategoryForm);
    }
    
    if (addCategoryBtnDesktop) {
        addCategoryBtnDesktop.addEventListener('click', showAddCategoryForm);
    }
    
    // Add event listeners for form controls
    const cancelBtn = document.getElementById('cancel-add-category');
    const cancelNewCategoryBtn = document.getElementById('cancel-new-category');
    const saveBtn = document.getElementById('save-new-category');
    
    if (cancelBtn) {
        cancelBtn.addEventListener('click', hideAddCategoryForm);
    }
    
    if (cancelNewCategoryBtn) {
        cancelNewCategoryBtn.addEventListener('click', hideAddCategoryForm);
    }
    
    if (saveBtn) {
        saveBtn.addEventListener('click', saveNewCategory);
    }
    
    // Add enter key support for category name input
    const categoryNameInput = document.getElementById('new-category-name');
    if (categoryNameInput) {
        categoryNameInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                saveNewCategory();
            }
        });
    }
}

/**
 * Show the add category form
 */
function showAddCategoryForm() {
    const form = document.getElementById('add-category-form');
    if (form) {
        form.classList.remove('hidden');
        categoryFormVisible = true;
        
        // Focus on the name input
        const nameInput = document.getElementById('new-category-name');
        if (nameInput) {
            nameInput.focus();
        }
        
        // Clear any previous values
        clearCategoryForm();
    }
}

/**
 * Hide the add category form
 */
function hideAddCategoryForm() {
    const form = document.getElementById('add-category-form');
    if (form) {
        form.classList.add('hidden');
        categoryFormVisible = false;
        clearCategoryForm();
        hideCategoryFormFeedback();
    }
}

/**
 * Clear the category form inputs
 */
function clearCategoryForm() {
    const nameInput = document.getElementById('new-category-name');
    const descriptionInput = document.getElementById('new-category-description');
    
    if (nameInput) nameInput.value = '';
    if (descriptionInput) descriptionInput.value = '';
}

/**
 * Save new category via AJAX
 */
function saveNewCategory() {
    const nameInput = document.getElementById('new-category-name');
    const descriptionInput = document.getElementById('new-category-description');
    
    if (!nameInput) return;
    
    const name = nameInput.value.trim();
    const description = descriptionInput ? descriptionInput.value.trim() : '';
    
    // Validate input
    if (!name) {
        showCategoryFormFeedback('error', 'Category name is required');
        nameInput.focus();
        return;
    }
    
    if (name.length > 255) {
        showCategoryFormFeedback('error', 'Category name must be less than 255 characters');
        nameInput.focus();
        return;
    }
    
    // Show loading state
    const saveBtn = document.getElementById('save-new-category');
    if (saveBtn) {
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Saving...';
    }
    
    showCategoryFormFeedback('info', 'Creating category...');
    
    // Prepare form data
    const formData = new FormData();
    formData.append('name', name);
    if (description) {
        formData.append('description', description);
    }
    
    // Make AJAX request
    fetch('/instructor/course-builder/categories', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showCategoryFormFeedback('success', 'Category created successfully!');
            
            // Add the new category to both dropdowns
            addCategoryToDropdowns(data.data);
            
            // Select the new category
            selectNewCategory(data.data.id);

            // Reinitialize auto-save for category dropdowns
            if (typeof initializeCategoryAutoSave === 'function') {
                initializeCategoryAutoSave();
            }

            // Hide the form after a short delay
            setTimeout(() => {
                hideAddCategoryForm();

                // Trigger auto-save for the course with the new category
                if (typeof autoSaveCourse === 'function') {
                    autoSaveCourse();
                }
            }, 1000);
            
        } else {
            showCategoryFormFeedback('error', data.message || 'Failed to create category');
            
            // Show validation errors if present
            if (data.errors) {
                const errorMessages = Object.values(data.errors).flat();
                showCategoryFormFeedback('error', errorMessages.join(', '));
            }
        }
    })
    .catch(error => {
        console.error('Error creating category:', error);
        showCategoryFormFeedback('error', 'Network error while creating category');
    })
    .finally(() => {
        // Reset save button
        if (saveBtn) {
            saveBtn.disabled = false;
            saveBtn.innerHTML = '<i class="fas fa-save mr-1"></i> Save';
        }
    });
}

/**
 * Add new category to all category dropdowns
 */
function addCategoryToDropdowns(category) {
    const selects = [
        document.getElementById('category-select'),
        document.getElementById('category-select-desktop')
    ];
    
    selects.forEach(select => {
        if (select) {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            
            // Insert in alphabetical order
            const options = Array.from(select.options);
            let inserted = false;
            
            for (let i = 1; i < options.length; i++) { // Start from 1 to skip "Select Category"
                if (options[i].textContent.toLowerCase() > category.name.toLowerCase()) {
                    select.insertBefore(option.cloneNode(true), options[i]);
                    inserted = true;
                    break;
                }
            }
            
            // If not inserted, append at the end
            if (!inserted) {
                select.appendChild(option.cloneNode(true));
            }
        }
    });
}

/**
 * Select the newly created category in all dropdowns
 */
function selectNewCategory(categoryId) {
    const selects = [
        document.getElementById('category-select'),
        document.getElementById('category-select-desktop')
    ];
    
    selects.forEach(select => {
        if (select) {
            select.value = categoryId;
            
            // Trigger change event to update any dependent elements
            const event = new Event('change', { bubbles: true });
            select.dispatchEvent(event);
        }
    });
}

/**
 * Show feedback message in the category form
 */
function showCategoryFormFeedback(type, message) {
    const feedback = document.getElementById('category-form-feedback');
    if (feedback) {
        feedback.classList.remove('hidden', 'text-green-500', 'text-red-500', 'text-blue-500');
        
        switch (type) {
            case 'success':
                feedback.classList.add('text-green-500');
                break;
            case 'error':
                feedback.classList.add('text-red-500');
                break;
            case 'info':
                feedback.classList.add('text-blue-500');
                break;
        }
        
        feedback.textContent = message;
    }
}

/**
 * Hide feedback message in the category form
 */
function hideCategoryFormFeedback() {
    const feedback = document.getElementById('category-form-feedback');
    if (feedback) {
        feedback.classList.add('hidden');
        feedback.textContent = '';
    }
}

/**
 * Refresh categories from server (useful for syncing)
 */
function refreshCategories() {
    return fetch('/instructor/course-builder/categories', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateCategoryDropdowns(data.data);
            return data.data;
        } else {
            throw new Error(data.message || 'Failed to fetch categories');
        }
    })
    .catch(error => {
        console.error('Error refreshing categories:', error);
        if (typeof showError === 'function') {
            showError('Failed to refresh categories');
        }
        throw error;
    });
}

/**
 * Update category dropdowns with fresh data
 */
function updateCategoryDropdowns(categories) {
    const selects = [
        document.getElementById('category-select'),
        document.getElementById('category-select-desktop')
    ];
    
    selects.forEach(select => {
        if (select) {
            const currentValue = select.value;
            
            // Clear existing options except the first one
            while (select.children.length > 1) {
                select.removeChild(select.lastChild);
            }
            
            // Add categories
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                select.appendChild(option);
            });
            
            // Restore selection if it still exists
            if (currentValue) {
                select.value = currentValue;
            }
        }
    });
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeCategoryManagement();
});
